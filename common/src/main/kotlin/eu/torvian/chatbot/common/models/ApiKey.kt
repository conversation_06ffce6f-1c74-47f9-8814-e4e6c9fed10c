package eu.torvian.chatbot.common.models

import kotlinx.serialization.Serializable

/**
 * Represents an API key configuration for an LLM provider. (Metadata only, no actual key material)
 *
 * @property id The unique identifier for the API key
 * @property name The display name for the API key
 * @property description Optional description providing additional context about the API key
 */
@Serializable
data class ApiKey(
    val id: String,
    val name: String,
    val description: String
)
