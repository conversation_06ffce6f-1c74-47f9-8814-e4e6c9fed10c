package eu.torvian.chatbot.server.service.error.apikey

/**
 * Represents possible errors when adding a new API key.
 */
sealed interface AddApiKeyError {
    /**
     * Indicates invalid input data for the API key (e.g., name or description format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : AddApiKeyError

    /**
     * Indicates that storing the credential failed.
     * This could happen if the credential storage system is unavailable.
     */
    data class CredentialStorageFailure(val reason: String) : AddApiKeyError

    /**
     * Indicates that an API key with the specified ID already exists.
     * This can happen if there's a collision in the credential alias generation.
     */
    data class ApiKeyAlreadyExists(val id: String) : AddApiKeyError
}
