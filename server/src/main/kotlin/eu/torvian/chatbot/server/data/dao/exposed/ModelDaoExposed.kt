package eu.torvian.chatbot.server.data.dao.exposed

import arrow.core.Either
import arrow.core.left
import arrow.core.raise.catch
import arrow.core.raise.either
import arrow.core.raise.ensure
import arrow.core.right
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.error.InsertModelError
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.UpdateModelError
import eu.torvian.chatbot.server.data.tables.LLMModelTable
import eu.torvian.chatbot.server.data.tables.mappers.toLLMModel
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import org.jetbrains.exposed.exceptions.ExposedSQLException
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

/**
 * Exposed implementation of the [ModelDao].
 */
class ModelDaoExposed(
    private val transactionScope: TransactionScope
) : ModelDao {
    override suspend fun getAllModels(): List<LLMModel> =
        transactionScope.transaction {
            LLMModelTable.selectAll()
                .map { it.toLLMModel() }
        }

    override suspend fun getModelById(id: Long): Either<ModelError.ModelNotFound, LLMModel> =
        transactionScope.transaction {
            LLMModelTable.selectAll().where { LLMModelTable.id eq id }
                .singleOrNull()
                ?.toLLMModel()
                ?.right()
                ?: ModelError.ModelNotFound(id).left()
        }

    override suspend fun getModelsByProviderId(providerId: Long): List<LLMModel> =
        transactionScope.transaction {
            LLMModelTable.selectAll().where { LLMModelTable.providerId eq providerId }
                .map { it.toLLMModel() }
        }

    override suspend fun insertModel(name: String, providerId: Long, active: Boolean, displayName: String?): Either<InsertModelError, LLMModel> =
        transactionScope.transaction {
            either {
                catch({
                    val insertStatement = LLMModelTable.insert {
                        it[LLMModelTable.name] = name
                        it[LLMModelTable.providerId] = providerId
                        it[LLMModelTable.active] = active
                        it[LLMModelTable.displayName] = displayName
                    }
                    insertStatement.resultedValues?.first()?.toLLMModel()
                        ?: throw IllegalStateException("Failed to retrieve newly inserted model")
                }) { e: ExposedSQLException ->
                    when {
                        e.isForeignKeyViolation() -> raise(InsertModelError.ProviderNotFound(providerId))
                        e.isUniqueConstraintViolation() -> raise(InsertModelError.ModelNameAlreadyExists(name))
                        else -> throw e
                    }
                }
            }
        }

    override suspend fun updateModel(model: LLMModel): Either<UpdateModelError, Unit> =
        transactionScope.transaction {
            either {
                catch({
                    val updatedRowCount = LLMModelTable.update({ LLMModelTable.id eq model.id }) {
                        it[name] = model.name
                        it[providerId] = model.providerId
                        it[active] = model.active
                        it[displayName] = model.displayName
                    }
                    ensure(updatedRowCount != 0) { UpdateModelError.ModelNotFound(model.id) }
                }) { e: ExposedSQLException ->
                    when {
                        e.isForeignKeyViolation() -> raise(UpdateModelError.ProviderNotFound(model.providerId))
                        e.isUniqueConstraintViolation() -> raise(UpdateModelError.ModelNameAlreadyExists(model.name))
                        else -> throw e
                    }
                }
            }
        }

    override suspend fun deleteModel(id: Long): Either<ModelError.ModelNotFound, Unit> =
        transactionScope.transaction {
            either {
                val deletedCount = LLMModelTable.deleteWhere { LLMModelTable.id eq id }
                ensure(deletedCount != 0) { ModelError.ModelNotFound(id) }
            }
        }


}
