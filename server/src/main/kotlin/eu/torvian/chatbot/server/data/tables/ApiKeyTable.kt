package eu.torvian.chatbot.server.data.tables

import eu.torvian.chatbot.common.models.ApiKey
import org.jetbrains.exposed.sql.Table

/**
 * Exposed table definition for API key configurations.
 * Corresponds to the [ApiKey] DTO.
 *
 * This table stores metadata about API keys, while the actual credentials
 * are stored securely via the CredentialManager system. The ID field serves
 * as both the primary key and the reference to the credential storage system.
 *
 * @property id The unique identifier (primary key) used to reference the credential
 * @property name The display name for the API key (not unique)
 * @property description Optional description providing context about the API key
 */
object ApiKeyTable : Table("api_keys") {
    val id = varchar("id", 255)
    val name = varchar("name", 255)
    val description = text("description")

    override val primaryKey = PrimaryKey(id)
}
