package eu.torvian.chatbot.server.service.error.apikey

/**
 * Represents possible errors when updating an existing API key.
 */
sealed interface UpdateApiKeyError {
    /**
     * Indicates that the API key with the specified ID was not found.
     * Maps from ApiKeyError.ApiKeyNotFound in the DAO layer.
     */
    data class ApiKeyNotFound(val id: String) : UpdateApiKeyError
    
    /**
     * Indicates invalid input data for the update (e.g., name or description format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : UpdateApiKeyError
}
