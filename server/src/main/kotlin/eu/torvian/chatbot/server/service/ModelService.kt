package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.LLMProviderType
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.service.error.model.AddModelError
import eu.torvian.chatbot.server.service.error.model.DeleteModelError
import eu.torvian.chatbot.server.service.error.model.GetModelError
import eu.torvian.chatbot.server.service.error.model.UpdateModelError
import eu.torvian.chatbot.server.service.error.provider.AddProviderError
import eu.torvian.chatbot.server.service.error.provider.DeleteProviderError
import eu.torvian.chatbot.server.service.error.provider.GetProviderError
import eu.torvian.chatbot.server.service.error.provider.UpdateProviderCredentialError
import eu.torvian.chatbot.server.service.error.provider.UpdateProviderError

import eu.torvian.chatbot.server.service.error.settings.AddSettingsError
import eu.torvian.chatbot.server.service.error.settings.DeleteSettingsError
import eu.torvian.chatbot.server.service.error.settings.GetSettingsByIdError
import eu.torvian.chatbot.server.service.error.settings.UpdateSettingsError

/**
 * Service interface for managing LLM Models and their Settings.
 */
interface ModelService {
    /**
     * Retrieves all LLM model configurations.
     */
    suspend fun getAllModels(): List<LLMModel>

    /**
     * Retrieves a single LLM model by its unique identifier.
     *
     * @param id The unique identifier of the LLM model to retrieve.
     * @return [Either] a [GetModelError.ModelNotFound] if the model doesn't exist, or the [LLMModel].
     */
    suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel>

    /**
     * Retrieves all LLM models associated with a specific provider.
     * @param providerId The ID of the provider to get models for.
     * @return A list of LLMModel entities associated with the provider.
     */
    suspend fun getModelsByProviderId(providerId: Long): List<LLMModel>

    /**
     * Adds a new LLM model configuration.
     * @param name The unique identifier for the model (e.g., "gpt-3.5-turbo").
     * @param providerId The ID of the provider that hosts this model.
     * @param active Whether the model is currently active and available for use.
     * @param displayName Optional display name for UI purposes.
     * @return Either an [AddModelError], or the newly created [LLMModel].
     */
    suspend fun addModel(name: String, providerId: Long, active: Boolean = true, displayName: String? = null): Either<AddModelError, LLMModel>

    /**
     * Updates an existing LLM model configuration.
     * Handles updating the API key if a new one is provided.
     * @param model The LLMModel object containing the updated values. The ID must match an existing model.
     * @return Either an [UpdateModelError] or Unit if successful.
     */
    suspend fun updateModel(model: LLMModel): Either<UpdateModelError, Unit>

    /**
     * Deletes an LLM model configuration.
     * Handles deletion of associated settings. Does not delete the API key itself.
     * @param id The ID of the model to delete.
     * @return Either a [DeleteModelError], or Unit if successful.
     */
    suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit>

    /**
     * Retrieves a specific settings profile by ID.
     * @param id The ID of the settings profile.
     * @return Either a [GetSettingsByIdError] if not found, or the [ModelSettings].
     */
    suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings>

    /**
     * Retrieves all settings profiles stored in the database.
     * @return A list of all [ModelSettings] objects.
     */
    suspend fun getAllSettings(): List<ModelSettings>

    /**
     * Retrieves all settings profiles associated with a specific LLM model.
     * @param modelId The ID of the LLM model.
     * @return A list of [ModelSettings] for the model, or an empty list if none exist.
     */
    suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings>

    /**
     * Creates a new settings profile with the specified parameters.
     *
     * @param name The display name of the settings profile (e.g., "Default", "Creative")
     * @param modelId The ID of the LLM model this settings profile is associated with
     * @param systemMessage Optional system message/prompt to include in the conversation context
     * @param temperature Optional sampling temperature for text generation
     * @param maxTokens Optional maximum number of tokens to generate in the response
     * @param customParamsJson Optional model-specific parameters stored as a JSON string
     * @return [Either] an [AddSettingsError] if the associated model doesn't exist or insertion fails, or the newly created [ModelSettings]
     */
    suspend fun addSettings(
        name: String,
        modelId: Long,
        systemMessage: String?,
        temperature: Float?,
        maxTokens: Int?,
        customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings>

    /**
     * Updates an existing settings profile with new values.
     *
     * @param settings The ModelSettings object containing the updated values. The ID must match an existing settings profile.
     * @return [Either] an [UpdateSettingsError] if not found or update fails, or [Unit] on success
     */
    suspend fun updateSettings(settings: ModelSettings): Either<UpdateSettingsError, Unit>

    /**
     * Deletes a settings profile with the specified ID.
     *
     * @param id The unique identifier of the settings profile to delete
     * @return [Either] a [DeleteSettingsError] if not found, or [Unit] on success
     */
    suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit>

    /**
     * Checks if an API key is configured for a specific model.
     * This checks if the model's provider has a valid API key configured.
     * @param modelId The ID of the model.
     * @return True if the model's provider has an API key configured, false otherwise.
     */
    suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean

    // --- LLM Provider Management ---

    /**
     * Retrieves all LLM provider configurations.
     */
    suspend fun getAllProviders(): List<LLMProvider>

    /**
     * Retrieves a single LLM provider configuration by its unique identifier.
     *
     * @param id The unique identifier of the provider to retrieve.
     * @return [Either] a [GetProviderError.ProviderNotFound] if the provider doesn't exist, or the [LLMProvider].
     */
    suspend fun getProviderById(id: Long): Either<GetProviderError.ProviderNotFound, LLMProvider>

    /**
     * Adds a new LLM provider configuration.
     * Creates a secure credential entry and associates it with the provided metadata.
     *
     * @param name The display name for the provider.
     * @param description The description for the provider.
     * @param baseUrl The base URL for the LLM API endpoint.
     * @param type The type of LLM provider.
     * @param credential The actual API key credential to store securely (null for local providers).
     * @return Either an [AddProviderError], or the newly created [LLMProvider].
     */
    suspend fun addProvider(name: String, description: String, baseUrl: String, type: LLMProviderType, credential: String?): Either<AddProviderError, LLMProvider>

    /**
     * Updates an existing LLM provider configuration.
     * Note: This updates all metadata. To update the credential, delete and recreate the provider.
     *
     * @param provider The LLMProvider object containing the updated values. The ID must match an existing provider.
     * @return Either an [UpdateProviderError] or Unit if successful.
     */
    suspend fun updateProvider(provider: LLMProvider): Either<UpdateProviderError, Unit>

    /**
     * Deletes an LLM provider configuration and its associated credential.
     * Checks if the provider is still in use by any models before deletion.
     *
     * @param id The ID of the provider to delete.
     * @return Either a [DeleteProviderError], or Unit if successful.
     */
    suspend fun deleteProvider(id: Long): Either<DeleteProviderError, Unit>

    /**
     * Updates the API key credential for an existing LLM provider.
     * This replaces the old credential with a new one and updates the provider's apiKeyId reference.
     *
     * @param providerId The ID of the provider to update.
     * @param newCredential The new API key credential to store securely. (null to remove the credential)
     * @return Either an [UpdateProviderCredentialError], or Unit if successful.
     */
    suspend fun updateProviderCredential(providerId: Long, newCredential: String?): Either<UpdateProviderCredentialError, Unit>
}
