package eu.torvian.chatbot.server.data.dao.error

/**
 * Represents possible domain-specific errors that can occur during ApiKey data operations.
 */
sealed interface ApiKeyError {
    /**
     * Indicates that an API key with the specified ID was not found.
     */
    data class ApiKeyNotFound(val id: String) : ApiKeyError

    /**
     * Indicates that an API key with the specified ID already exists.
     */
    data class ApiKeyAlreadyExists(val id: String) : ApiKeyError
}
