package eu.torvian.chatbot.server.data.dao.error

/**
 * Represents possible errors when updating an existing LLM provider.
 */
sealed interface UpdateProviderError {
    /**
     * Indicates that the provider with the specified ID was not found.
     */
    data class ProviderNotFound(val id: Long) : UpdateProviderError
    
    /**
     * Indicates that an LLM provider with the specified API key ID already exists (and it's not the same provider being updated).
     */
    data class ApiKeyAlreadyInUse(val apiKeyId: String) : UpdateProviderError
}
