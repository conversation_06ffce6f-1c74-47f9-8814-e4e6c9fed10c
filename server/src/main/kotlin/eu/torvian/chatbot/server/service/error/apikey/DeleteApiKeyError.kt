package eu.torvian.chatbot.server.service.error.apikey

/**
 * Represents possible errors when deleting an API key.
 */
sealed interface DeleteApiKeyError {
    /**
     * Indicates that the API key with the specified ID was not found.
     * Maps from ApiKeyError.ApiKeyNotFound in the DAO layer.
     */
    data class ApiKeyNotFound(val id: String) : DeleteApiKeyError
    
    /**
     * Indicates that the API key cannot be deleted because it is still in use.
     * This occurs when models are still referencing this API key.
     */
    data class ApiKeyInUse(val id: String, val modelNames: List<String>) : DeleteApiKeyError
}
